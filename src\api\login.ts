// import http from './http';
import { ws } from './websocket';
import { FunctionCode } from './websocket';
import type { LoginResponse } from '@/types/code';

class LoginService {
  /**
   * WebSocket方式登录
   * @param username 用户名
   * @param password 密码
   * @returns Promise 登录成功时resolve用户信息
   */
  static loginByWS(username: string, password: string) {
    return ws.register(
      FunctionCode.登录,
      {
        loginType: 1,
        branchId: '5101',
        orgId: '5101',
        credit: 0,
        username,
        password,
        mac: '',
        os: '',
        md5: '',
        sessionId: '0',
        configStr: '',
      },
      6,
    ) as Promise<LoginResponse>;
  }

  /**
   * WebSocket方式登出
   */
  static logoutByWS() {
    return ws.register(FunctionCode.登出);
  }
}

export default LoginService;
