import { FunctionCode } from '@/api/websocket';

interface CommonWsResponse {
  errorCode: number;
  errorMsg?: string;
}

export interface LoginResponse extends CommonWsResponse {
  fullName: string;
  orgId: number;
  orgName: string;
  roleId: number;
  token: string;
  userId: number;
  userType: number;
  username: string;
}

export interface LoginBody {
  loginType: number;
  branchId: string;
  orgId: string;
  credit: number;
  username: string;
  password: string;
  mac: string;
  os: string;
  md5: string;
  sessionId: string;
  configStr: string;
}

export interface TokenResponse extends CommonWsResponse {
  token: string;
}

export type FunctionCodeBody = {
  [FunctionCode.登录]: LoginBody;
  [FunctionCode.登录响应]: LoginResponse;
  [FunctionCode.请求TOKEN相应]: TokenResponse;
  // 下面无实际作用，补完是为了通过类型验证
  [FunctionCode.请求TOKEN]: CommonWsResponse;
  [FunctionCode.登出]: CommonWsResponse;
  [FunctionCode.心跳]: CommonWsResponse;
};
